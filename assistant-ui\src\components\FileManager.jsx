import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faFile, faBoxOpen, faRobot, faPlus, faFolder, faRefresh, 
  faEdit, faTrash, faExclamationTriangle, faChevronDown,
  faChevronUp, faFolderOpen, faPalette, faEye
} from '@fortawesome/free-solid-svg-icons';
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from "./ui/card";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Label } from "./ui/label";

const FileManager = ({ basePath, sections, onPathSelected, title, description, enableCascadeView }) => {
  const [sectionFiles, setSectionFiles] = useState({});
  const [sectionMetadata, setSectionMetadata] = useState({});
  const [loadingFiles, setLoadingFiles] = useState(false);
  const [error, setError] = useState(null);
  const [expandedSections, setExpandedSections] = useState({});

  // Initialize section files state and expanded state
  useEffect(() => {
    if (sections) {
      const initialState = {};
      const initialMetadata = {};
      const initialExpandedState = {};
      sections.forEach(section => {
        initialState[section.key] = [];
        initialMetadata[section.key] = null;
        initialExpandedState[section.key] = false; // Start collapsed
      });
      setSectionFiles(initialState);
      setSectionMetadata(initialMetadata);
      setExpandedSections(initialExpandedState);
    }
  }, [sections]);

  // Load files when path changes
  useEffect(() => {
    if (basePath && sections) {
      loadSectionFiles(basePath);
    }
  }, [basePath, sections]);

  const loadSectionFiles = async (path) => {
    if (!path || !sections) {
      console.log('⚠️ loadSectionFiles called but missing path or sections:', { path, sections });
      return;
    }
    
    setLoadingFiles(true);
    setError(null);
    
    try {
      console.log('🔍 Loading files from:', path);
      console.log('📁 Sections to load:', sections.map(s => ({ key: s.key, name: s.name, folderNames: s.folderNames, fileExtensions: s.fileExtensions })));
      
      // Load files from each configured section
      const sectionPromises = sections.map(section => 
        loadFilesFromFolderVariants(path, section.folderNames || [section.key], section.fileExtensions || ['.json', '.js', '.jsx'])
      );
      
      const results = await Promise.allSettled(sectionPromises);
      
      const newSectionFiles = {};
      sections.forEach((section, index) => {
        newSectionFiles[section.key] = results[index].status === 'fulfilled' ? results[index].value : [];
      });
      
      setSectionFiles(newSectionFiles);

      // Load metadata for sections that have it
      await loadSectionMetadata(path);

      console.log('✅ Loaded files:', newSectionFiles);

    } catch (error) {
      console.error('❌ Error loading files:', error);
      setError('Failed to load files. Please check the directory path.');
    } finally {
      setLoadingFiles(false);
    }
  };

  const loadFilesFromFolderVariants = async (basePath, folderNames, fileExtensions) => {
    if (!basePath || !folderNames || !fileExtensions) {
      console.warn('Missing required parameters in loadFilesFromFolderVariants');
      return [];
    }

    try {
      // Try each folder name to find valid files
      let allFiles = [];
      
      for (const folderName of folderNames) {
        try {
          const folderPath = `${basePath}\\${folderName}`;
          const result = await invoke('browse_directory', { path: folderPath });
          
          // Process directories
          if (result.directories && Array.isArray(result.directories)) {
            const folders = result.directories.map(dir => ({
              name: typeof dir === 'string' ? dir : dir.name || 'Unknown',
              path: typeof dir === 'string' ? `${folderPath}\\${dir}` : dir.path || `${folderPath}\\${dir.name || 'Unknown'}`,
              isDirectory: true
            }));
            allFiles = [...allFiles, ...folders];
          }
          
          // Process files
          if (result.files && Array.isArray(result.files)) {
            const files = result.files
              .filter(file => {
                const fileName = typeof file === 'string' ? file : file.name || '';
                return fileExtensions.some(ext => fileName.toLowerCase().endsWith(ext.toLowerCase()));
              })
              .map(file => {
                const fileName = typeof file === 'string' ? file : file.name || 'Unknown';
                const extension = fileName.lastIndexOf('.') > -1 ? fileName.substring(fileName.lastIndexOf('.')) : '';
                return {
                  name: fileName,
                  path: typeof file === 'string' ? `${folderPath}\\${file}` : file.path || `${folderPath}\\${file.name || 'Unknown'}`,
                  isDirectory: false,
                  extension: extension,
                  size: typeof file === 'object' && file.size ? file.size : 0
                };
              });
            allFiles = [...allFiles, ...files];
          }
        } catch (err) {
          console.log(`Folder ${folderName} does not exist or cannot be accessed:`, err);
          // Continue with next folder - this is not an error, just a folder that doesn't exist
        }
      }
      
      return allFiles;
    } catch (error) {
      console.error('Error loading files from folder variants:', error);
      return [];
    }
  };

  const loadSectionMetadata = async (path) => {
    try {
      // For each section with hasMetadata, try to load its metadata file
      const metadataPromises = sections
        .filter(section => section.hasMetadata)
        .map(async section => {
          try {
            // Construct path to metadata file (e.g., systemPrompts.json)
            const metadataPath = `${path}\\${section.key}.json`;
            const metadata = await invoke('read_json_file', { path: metadataPath }).catch(() => null);
            return { key: section.key, metadata };
          } catch (err) {
            console.log(`No metadata file found for ${section.key}:`, err);
            return { key: section.key, metadata: null };
          }
        });

      const results = await Promise.allSettled(metadataPromises);
      const newMetadata = {};

      results.forEach(result => {
        if (result.status === 'fulfilled' && result.value) {
          newMetadata[result.value.key] = result.value.metadata;
        }
      });

      setSectionMetadata(newMetadata);
    } catch (error) {
      console.error('Error loading section metadata:', error);
    }
  };

  const findFileInMetadata = (metadata, fileName) => {
    if (!metadata || !metadata.items || !Array.isArray(metadata.items)) {
      return null;
    }
    
    return metadata.items.find(item => 
      item.fileName === fileName || item.path?.endsWith(fileName)
    );
  };

  // Toggle section expanded/collapsed state
  const toggleSectionExpanded = (sectionKey) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionKey]: !prev[sectionKey]
    }));
  };

  const handleCreateNew = async (section, sectionKey) => {
    console.log(`Create new ${section.name} clicked`);
    if (section.onCreate) {
      section.onCreate(sectionKey);
    } else {
      // Default behavior
      alert(`Creating new ${section.name} - TODO: Implement file creation dialog`);
    }
  };

  const handleEditFile = async (file, section, sectionKey) => {
    console.log(`Edit ${section.name} file:`, file);
    if (section.onEdit) {
      section.onEdit(file, sectionKey);
    } else {
      // Default behavior
      alert(`Edit ${file.name} - TODO: Implement file editing`);
    }
  };

  const handleDeleteFile = async (file, section, sectionKey) => {
    if (window.confirm(`Are you sure you want to delete ${file.name}?`)) {
      try {
        console.log(`Delete ${section.name} file:`, file);
        if (section.onDelete) {
          await section.onDelete(file, sectionKey);
          // Refresh files after deletion
          await loadSectionFiles(basePath);
        } else {
          // Default behavior
          alert(`Delete ${file.name} - TODO: Implement file deletion`);
        }
      } catch (error) {
        console.error('Error deleting file:', error);
        alert('Failed to delete file');
      }
    }
  };

  // View file content using FileViewer
  const handleViewFile = async (file, section, sectionKey) => {
    console.log(`View ${section.name} file:`, file);
    if (section.onView) {
      section.onView(file, sectionKey);
    } else {
      // Default behavior - open in FileViewer
      alert(`View ${file.name} - TODO: Implement file viewing with FileViewer`);
    }
  };

  const renderFileSection = (section) => {
    const files = sectionFiles[section.key] || [];
    const metadata = sectionMetadata[section.key];
    const isExpanded = expandedSections[section.key];
    
    // Count of folders and files
    const folderCount = files.filter(f => f.isDirectory).length;
    const fileCount = files.length - folderCount;
    
    return (
      <Card key={section.key} className="overflow-hidden mb-2">
        <CardHeader className="py-2 px-3">
          <div className="flex justify-between items-center">
            <CardTitle className="flex items-center text-base">
              <FontAwesomeIcon icon={section.icon} className={`h-4 w-4 text-${section.color}-500 mr-2`} />
              <span>{section.name}</span>
              {section.cascadeLevel && enableCascadeView && (
                <span className={`ml-2 px-1 py-0.5 text-xs rounded-full bg-${section.color}-100 text-${section.color}-700`}>
                  L{section.cascadeLevel}
                </span>
              )}
            </CardTitle>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => toggleSectionExpanded(section.key)}
              aria-label={isExpanded ? "Collapse" : "Expand"}
              className="h-6 w-6 p-0"
            >
              <FontAwesomeIcon icon={isExpanded ? faChevronUp : faChevronDown} className="h-3 w-3" />
            </Button>
          </div>
          {section.description && (
            <CardDescription className="text-xs mt-0">
              {section.description}
            </CardDescription>
          )}
        </CardHeader>
        
        <CardContent className="py-2 px-3">
          {/* Summary view - always visible */}
          <div className="flex items-center justify-between text-xs bg-slate-50 dark:bg-slate-900/50 rounded-md p-1 mb-2">
            <div className="flex items-center">
              <span className="font-medium">{files.length}</span> items
              <span className="text-muted-foreground ml-1">
                ({folderCount} folders, {fileCount} files)
              </span>
            </div>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => handleCreateNew(section, section.key)}
              className="text-xs h-6 px-2"
            >
              <FontAwesomeIcon icon={faPlus} className="mr-1 h-2 w-2" />
              Add
            </Button>
          </div>
          
          {/* Expanded view - only visible when expanded */}
          {isExpanded && (
            <div className="space-y-1 mt-1">
              {loadingFiles ? (
                <div className="text-center py-1 text-xs text-gray-500">
                  <FontAwesomeIcon icon={faRefresh} className="animate-spin mr-1 h-3 w-3" />
                  Loading...
                </div>
              ) : files.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-1">
                  {files.map((file, index) => (
                    <div key={`${section.key}-${file.name}-${index}`} className="p-1 hover:bg-slate-100 dark:hover:bg-slate-800/50 rounded-sm transition-colors">
                      <div className="flex items-start space-x-1">
                        <FontAwesomeIcon 
                          icon={file.isDirectory ? faFolder : section.icon} 
                          className={`h-3 w-3 ${file.isDirectory ? 'text-yellow-500' : `text-${section.color}-500`} mt-0.5`} 
                        />
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-xs truncate">{file.name}</div>
                          <div className="flex items-center gap-1 mt-0.5">
                            {!file.isDirectory && (
                              <button 
                                className={`text-[10px] text-${section.color}-600 hover:text-${section.color}-800 flex items-center`}
                                onClick={() => handleViewFile(file, section, section.key)}
                              >
                                <FontAwesomeIcon icon={faEye} className="mr-0.5 h-2 w-2" />
                                View
                              </button>
                            )}
                            <button 
                              className={`text-[10px] text-${section.color}-600 hover:text-${section.color}-800 flex items-center`}
                              onClick={() => handleEditFile(file, section, section.key)}
                            >
                              <FontAwesomeIcon icon={faEdit} className="mr-0.5 h-2 w-2" />
                              Edit
                            </button>
                            <button 
                              className="text-[10px] text-red-600 hover:text-red-800 flex items-center"
                              onClick={() => handleDeleteFile(file, section, section.key)}
                            >
                              <FontAwesomeIcon icon={faTrash} className="mr-0.5 h-2 w-2" />
                              Delete
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-2 text-xs text-gray-500 bg-slate-50/50 dark:bg-slate-900/50 rounded-sm">
                  <p>No {section.key} files found</p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  // Render the folder selector with side-by-side layout
  const renderFolderSelector = () => {
    return (
      <div className="mb-2">
        <div className="flex items-center space-x-2">
          <div className="flex-1 relative">
            <Input 
              value={basePath}
              readOnly
              className="pr-24 font-mono text-xs bg-slate-50 dark:bg-slate-800"
            />
            <Button
              onClick={() => onPathSelected && onPathSelected(basePath)}
              className="absolute right-1 top-1 bottom-1 text-xs h-6 px-2"
              size="sm"
              aria-label="Select Folder"
            >
              <FontAwesomeIcon icon={faFolderOpen} className="mr-1 h-3 w-3" />
              Select
            </Button>
          </div>
        </div>
      </div>
    );
  };

  if (!basePath) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <FontAwesomeIcon icon={faExclamationTriangle} className="h-12 w-12 text-yellow-500 mb-4" />
          <h3 className="text-lg font-semibold mb-2">{title || 'No Directory Selected'}</h3>
          <p className="text-muted-foreground">{description || 'Please select a directory first to manage files.'}</p>
        </CardContent>
      </Card>
    );
  }

  if (!sections || sections.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <FontAwesomeIcon icon={faExclamationTriangle} className="h-12 w-12 text-yellow-500 mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Sections Configured</h3>
          <p className="text-muted-foreground">No file sections have been configured for this directory.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {error && (
        <Card className="border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800">
          <CardContent className="p-4">
            <div className="flex items-center text-red-600 dark:text-red-400">
              <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
              {error}
            </div>
          </CardContent>
        </Card>
      )}
      
      {onPathSelected && renderFolderSelector()}
      
      <div className="space-y-4">
        {sections.map(section => renderFileSection(section))}
      </div>
    </div>
  );
};

export default FileManager;