import React, { useState, useEffect, useMemo } from 'react';
import ErrorPopup from './ErrorPopup';
import ErrorIndicator from './ErrorIndicator';
import { useIsMounted } from '../hooks/useIsMounted.js';
import { invoke } from '@tauri-apps/api/core';

// Import our modular plugin components with correct versions
const PluginComponents = {
  'Chat': React.lazy(() => import('../../../Storage/Addons/Plugins/Chat/versions/v2.0.0/chat.jsx')),
  'Browser': React.lazy(() => import('../../../Storage/Addons/Plugins/Browser/versions/v1.0.0/browser.jsx')),
  'File Explorer': React.lazy(() => import('../../../Storage/Addons/Plugins/FileExplorer/versions/v1.0.0/explorer.jsx')),
  // FloatingChat removed as it was not rendering correctly and activating unexpectedly
};

// Outer ErrorBoundary for handling errors in Suspense boundaries
class OuterErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Plugin Outer ErrorBoundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      // Render a minimal error display instead of blocking the entire UI
      return (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md m-2">
          <div className="flex items-center">
            <ErrorIndicator error={this.state.error} />
            <div className="ml-2">
              <h3 className="text-sm font-medium text-red-800">Plugin Loading Error</h3>
              <p className="text-xs text-red-700 mt-1">
                Failed to load plugin. The rest of the application remains functional.
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Inner ErrorBoundary for handling errors in the loaded component
class InnerErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Plugin Inner ErrorBoundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      // Render a minimal error display instead of blocking the entire UI
      return (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md m-2">
          <div className="flex items-center">
            <ErrorIndicator error={this.state.error} />
            <div className="ml-2">
              <h3 className="text-sm font-medium text-red-800">Plugin Error</h3>
              <p className="text-xs text-red-700 mt-1">
                An error occurred in this plugin. Other parts of the application remain functional.
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Simple ErrorBoundary component (kept for backward compatibility)
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Plugin Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      // Render a minimal error display instead of blocking the entire UI
      return (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md m-2">
          <div className="flex items-center">
            <ErrorIndicator error={this.state.error} />
            <div className="ml-2">
              <h3 className="text-sm font-medium text-red-800">Plugin Error</h3>
              <p className="text-xs text-red-700 mt-1">
                An error occurred in this plugin. Other parts of the application remain functional.
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

const PluginLoader = ({ pluginName, version = 'v1.0.0', ...props }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const isMounted = useIsMounted();
  
  // Memoize the component to prevent unnecessary re-creations
  const PluginComponent = useMemo(() => {
    return PluginComponents[pluginName];
  }, [pluginName]);

  useEffect(() => {
    let cancelled = false;
    
    const loadPlugin = async () => {
      try {
        // Check if component is still mounted
        if (cancelled || !isMounted()) return;
        
        setLoading(true);
        setError(null);

        // First, check if it's a built-in plugin component
        if (!PluginComponent) {
          // For dynamic loading, we would load from the file system
          // For now, we'll handle the core plugins directly
          console.warn(`Plugin ${pluginName} not found in built-in components`);
          if (!cancelled && isMounted()) {
            setError(`Plugin ${pluginName} not available`);
          }
          return;
        }

        // Component is available, we can proceed
        if (!cancelled && isMounted()) {
          setLoading(false);
        }
      } catch (err) {
        console.error('Error loading plugin:', err);
        if (!cancelled && isMounted()) {
          setError(`Failed to load plugin: ${err.message}`);
        }
      } finally {
        if (!cancelled && isMounted()) {
          setLoading(false);
        }
      }
    };

    loadPlugin();
    
    // Cleanup function to prevent state updates on unmounted components
    return () => {
      cancelled = true;
    };
  }, [pluginName, version, isMounted, PluginComponent]);

  // Handle loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-slate-600 dark:text-slate-400">Loading {pluginName}...</p>
        </div>
      </div>
    );
  }

  // Handle error state with a non-blocking display
  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-md m-2">
        <div className="flex items-center">
          <ErrorIndicator error={new Error(error)} />
          <div className="ml-2">
            <h3 className="text-sm font-medium text-red-800">Plugin Error</h3>
            <p className="text-xs text-red-700 mt-1">
              Failed to load the {pluginName} plugin. Other parts of the application remain functional.
            </p>
            <button 
              onClick={() => window.location.reload()}
              className="mt-2 px-3 py-1 bg-red-100 text-red-800 text-xs rounded hover:bg-red-200 transition-colors"
            >
              Reload
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Handle missing component
  if (!PluginComponent) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-md m-2">
        <div className="flex items-center">
          <ErrorIndicator />
          <div className="ml-2">
            <h3 className="text-sm font-medium text-red-800">Plugin Not Found</h3>
            <p className="text-xs text-red-700 mt-1">
              The {pluginName} plugin is not available. Other parts of the application remain functional.
            </p>
            <button 
              onClick={() => window.location.reload()}
              className="mt-2 px-3 py-1 bg-red-100 text-red-800 text-xs rounded hover:bg-red-200 transition-colors"
            >
              Reload
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Render the component with proper error boundaries and suspense
  // Using a more robust approach to handle the Suspense boundary
  return (
    <OuterErrorBoundary>
      <React.Suspense 
        fallback={
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-slate-600 dark:text-slate-400">Starting {pluginName}...</p>
            </div>
          </div>
        }
      >
        <InnerErrorBoundary>
          <PluginComponent {...props} />
        </InnerErrorBoundary>
      </React.Suspense>
    </OuterErrorBoundary>
  );
};

export default PluginLoader;