import React, { useState, useEffect, useCallback, memo, useRef } from 'react';
import { Button } from "./ui/button";
import ErrorPopup from './ErrorPopup';
import { useIsMounted } from '../hooks/useIsMounted';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus, faTimes, faGlobe, faComments, faFolder, faNoteSticky, faCloud, faWrench, faCode, faSearch } from '@fortawesome/free-solid-svg-icons';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { invoke } from '@tauri-apps/api/core';

// Simple ErrorBoundary component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('TabSystem Error:', error, errorInfo);
  }

  componentDidUpdate(prevProps) {
    if (prevProps.children !== this.props.children && this.state.hasError) {
      this.setState({ hasError: false, error: null });
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <ErrorPopup
          title="Tab System Error"
          message="An error occurred in the tab system. Please try reloading the page."
          error={this.state.error}
          onReload={() => window.location.reload()}
        />
      );
    }
    return this.props.children;
  }
}

/**
 * TabSystem component for managing application tabs
 * @param {TabSystemProps} props - Component props
 */
const TabSystem = React.memo(({ tabs = [], addTab = () => {}, closeTab = () => {} }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [showAppMenu, setShowAppMenu] = useState(false);
  const [availableApps, setAvailableApps] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const isMounted = useIsMounted();
  const abortControllerRef = useRef(null);
  const menuRef = useRef(null);
  
  // Initialize the abort controller in an effect
  useEffect(() => {
    abortControllerRef.current = new AbortController();
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Handle clicks outside menu with cleanup
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setShowAppMenu(false);
      }
    };

    // Use capture phase to catch events before they bubble up
    document.addEventListener('mousedown', handleClickOutside, true);
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside, true);
    };
  }, []);

  // Load available apps from plugin system with error handling and cleanup
  useEffect(() => {
    let isActive = true;
    
    const loadAvailableApps = async () => {
      if (!isActive || !isMounted()) return;
      
      // Don't create a new abort controller here, use the one from the effect
      if (!abortControllerRef.current) return;
      
      const { signal } = abortControllerRef.current;
      
      try {
        setLoading(true);
        
        // Get all plugins from the backend with abort signal
        const plugins = await invoke('get_plugins', {}, { signal }).catch(err => {
          if (err.name === 'AbortError') return null;
          console.warn('Failed to load plugins, using fallback:', err);
          return null;
        });
        
        if (!isActive || !isMounted()) return;
        
        // Process plugins or use fallback
        const apps = (Array.isArray(plugins) ? plugins : [])
          .filter(plugin => plugin?.enabled && (plugin.plugin_type === 'ui_component' || plugin.app_type))
          .map(plugin => {
            // Map plugin names to appropriate icons
            let icon;
            switch ((plugin.name || '').toLowerCase()) {
              case 'chat':
                icon = faComments;
                break;
              case 'file explorer':
                icon = faFolder;
                break;
              case 'notes':
                icon = faNoteSticky;
                break;
              case 'cloud':
                icon = faCloud;
                break;
              case 'tools':
                icon = faWrench;
                break;
              case 'developer':
                icon = faCode;
                break;
              case 'browser':
                icon = faGlobe;
                break;
              default:
                icon = faGlobe;
            }
            
            return {
              id: `plugin-${plugin.id || plugin.name}`,
              name: plugin.name || 'Unknown App',
              icon,
              path: `/app/${plugin.id || (plugin.name || '').toLowerCase().replace(/\s+/g, '-')}`,
              description: plugin.description || '',
              app_type: plugin.app_type || 'plugin',
              plugin_type: plugin.plugin_type,
              component: plugin.name || 'Unknown App'  // This should match the key in PluginComponents
            };
          });
          
        if (!isActive || !isMounted()) return;
        
        setAvailableApps(apps);
        setError(null);
      } catch (error) {
        if (error.name !== 'AbortError' && isActive && isMounted()) {
          console.error('Error loading apps:', error);
          setError(error);
        }
      } finally {
        if (isActive && isMounted()) {
          setLoading(false);
        }
      }
    };
    
    loadAvailableApps();
    
    return () => {
      isActive = false;
      // Don't abort the controller here, let the main effect cleanup handle it
    };
  }, [isMounted]);

  // Cleanup function for abort controller and other resources
  useEffect(() => {
    let isMounted = true;
    
    return () => {
      isMounted = false;
      
      // Abort any pending requests
      const controller = abortControllerRef.current;
      if (controller) {
        controller.abort();
      }
      
      // Clean up any other resources if needed
      if (menuRef.current) {
        // No need to manually remove the element from DOM as React manages it
        // Just clean up any custom event listeners here if needed
      }  
      menuRef.current = null;
    };
  }, []);

  // Show error if any
  if (error) {
    return (
      <div className="p-4 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-300 rounded-md">
        <p>Failed to load tab system: {error.message}</p>
        <button 
          onClick={() => window.location.reload()}
          className="mt-2 px-3 py-1 bg-red-100 dark:bg-red-800/50 rounded hover:bg-red-200 dark:hover:bg-red-700/50"
        >
          Reload
        </button>
      </div>
    );
  }

  const getAvailableApps = useCallback((type) => {
    if (!Array.isArray(availableApps)) return [];
    if (type) {
      return availableApps.filter(app => app.app_type === type);
    }
    return availableApps;
  }, [availableApps]);

  const handleCloseTab = useCallback((e, tabId) => {
    if (!e || !tabId) {
      console.warn('Invalid parameters for handleCloseTab:', { e, tabId });
      return;
    }
    
    e.stopPropagation();
    e.preventDefault();
    
    // Get the current active tab before closing
    const currentTab = tabs.find(tab => tab.path === location.pathname);
    const isClosingActiveTab = currentTab && currentTab.id === tabId;
    
    // Close the tab
    if (typeof closeTab === 'function') {
      closeTab(tabId);
    }
    
    // If we're closing the active tab, navigate to the first available tab or home
    if (isClosingActiveTab) {
      const remainingTabs = tabs.filter(tab => tab.id !== tabId);
      const newActiveTab = remainingTabs[0];
      
      if (newActiveTab) {
        navigate(newActiveTab.path, { replace: true });
      } else {
        navigate('/', { replace: true });
      }
    }
  }, [closeTab, tabs, location.pathname, navigate]);

  // Memoize the tab rendering to prevent unnecessary re-renders
  const renderTabs = useCallback(() => {
    if (!Array.isArray(tabs)) return null;
    
    // Create a stable reference for the current path to avoid recreation on each render
    const currentPath = location.pathname;
    
    return (
      <div className="flex items-center">
        {tabs
          .filter(tab => tab && tab.id && tab.path && tab.name) // Defensive filtering
          .map((tab) => {
            const isActive = currentPath === tab.path;
            const showCloseButton = !['dashboard', 'chat', 'tools', 'settings'].includes(tab.id);
            
            return (
              <div 
                key={`tab-${tab.id}`} 
                className={`relative group ${isActive ? 'z-10' : ''}`}
                data-tab-id={tab.id}
              >
                <Link
                  to={tab.path}
                  className={`flex items-center px-3 py-1.5 cursor-pointer transition-all duration-200 relative ${
                    isActive
                      ? 'text-blue-600 dark:text-blue-400'
                      : 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-200'
                  }`}
                  onClick={(e) => {
                    e.preventDefault();
                    if (isMounted() && currentPath !== tab.path) {
                      navigate(tab.path);
                    }
                  }}
                >
                  {isActive && (
                    <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500"></div>
                  )}
                  <FontAwesomeIcon icon={tab.icon} className="mr-1.5 h-3 w-3 flex-shrink-0" />
                  <span className="text-xs font-medium truncate max-w-[120px]">{tab.name}</span>
                  
                  {showCloseButton && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="ml-2 h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-slate-200 dark:hover:bg-slate-700"
                      onClick={(e) => {
                        e.stopPropagation();
                        e.nativeEvent.stopImmediatePropagation();
                        handleCloseTab(e, tab.id);
                      }}
                      aria-label={`Close ${tab.name}`}
                    >
                      <FontAwesomeIcon icon={faTimes} className="h-3 w-3" />
                    </Button>
                  )}
                </Link>
              </div>
            );
          })}
      </div>
    );
  }, [tabs, location.pathname, isMounted, navigate, handleCloseTab]);

  // Memoize the app menu content to prevent unnecessary re-renders
  const renderAppMenu = useCallback(() => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-6">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
          <span className="ml-2 text-sm text-slate-500 dark:text-slate-400">Loading apps...</span>
        </div>
      );
    }

    return (
      <div className="max-h-96 overflow-y-auto">
        {/* Core Tools */}
        {getAvailableApps().filter(app => app.app_type === 'core-tool').length > 0 && (
          <>
            <div className="px-3 py-2 text-xs font-medium text-slate-600 dark:text-slate-300 bg-slate-50 dark:bg-slate-700/50">
              Core Tools
            </div>
            {getAvailableApps()
              .filter(app => app.app_type === 'core-tool')
              .map((app) => (
                <button
                  key={`app-${app.id}`}
                  className="flex items-start w-full px-4 py-3 text-left hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors text-slate-700 dark:text-slate-300"
                  onClick={() => {
                    if (isMounted()) {
                      addTab(app);
                      setShowAppMenu(false);
                    }
                  }}
                >
                  <FontAwesomeIcon icon={app.icon} className="mr-3 h-4 w-4 text-blue-500 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{app.name}</div>
                    {app.description && (
                      <div className="text-xs text-slate-500 dark:text-slate-400 mt-1 line-clamp-2">
                        {app.description}
                      </div>
                    )}
                  </div>
                </button>
              ))}
            </>
          )}
          
          {/* Other Apps */}
          {getAvailableApps().filter(app => app.app_type !== 'core-tool').length > 0 && (
            <>
              <div className="px-3 py-2 text-xs font-medium text-slate-600 dark:text-slate-300 bg-slate-50 dark:bg-slate-700/50">
                Applications
              </div>
              {getAvailableApps()
                .filter(app => app.app_type !== 'core-tool')
                .map((app) => (
                  <button
                    key={`app-${app.id}`}
                    className="flex items-start w-full px-4 py-3 text-left hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors text-slate-700 dark:text-slate-300"
                    onClick={() => {
                      if (isMounted()) {
                        addTab(app);
                        setShowAppMenu(false);
                      }
                    }}
                  >
                    <FontAwesomeIcon 
                      icon={app.icon} 
                      className={`mr-3 h-4 w-4 mt-0.5 flex-shrink-0 ${
                        app.plugin_type === 'python' ? 'text-yellow-500' : 'text-slate-500 dark:text-slate-400'
                      }`} 
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="font-medium truncate">{app.name}</span>
                        {app.plugin_type === 'python' && (
                          <span className="text-xs bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 px-1.5 py-0.5 rounded">
                            Python
                          </span>
                        )}
                      </div>
                      {app.description && (
                        <div className="text-xs text-slate-500 dark:text-slate-400 mt-1 line-clamp-2">
                          {app.description}
                        </div>
                      )}
                    </div>
                  </button>
                ))}
            </>
          )}
          
          {!loading && getAvailableApps().length === 0 && (
            <div className="px-4 py-6 text-center text-slate-500 dark:text-slate-400">
              <FontAwesomeIcon icon={faWrench} className="h-8 w-8 mb-2 opacity-50" />
              <div className="text-sm">No applications available</div>
            </div>
          )}
      </div>
    );
  }, [loading, getAvailableApps, isMounted, addTab]);

  const renderAppMenuContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-6">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
          <span className="ml-2 text-sm text-slate-500 dark:text-slate-400">Loading apps...</span>
        </div>
      );
    }

    return (
      <div className="max-h-96 overflow-y-auto">
        {/* Core Tools */}
        {getAvailableApps().filter(app => app.app_type === 'core-tool').length > 0 && (
          <>
            <div className="px-3 py-2 text-xs font-medium text-slate-600 dark:text-slate-300 bg-slate-50 dark:bg-slate-700/50">
              Core Tools
            </div>
            {getAvailableApps()
              .filter(app => app.app_type === 'core-tool')
              .map((app) => (
                <button
                  key={`app-${app.id}`}
                  className="flex items-start w-full px-4 py-3 text-left hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors text-slate-700 dark:text-slate-330"
                  onClick={() => {
                    if (isMounted()) {
                      addTab(app);
                      setShowAppMenu(false);
                    }
                  }}
                >
                  <FontAwesomeIcon icon={app.icon} className="mr-3 h-4 w-4 text-blue-500 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{app.name}</div>
                    {app.description && (
                      <div className="text-xs text-slate-500 dark:text-slate-400 mt-1 line-clamp-2">
                        {app.description}
                      </div>
                    )}
                  </div>
                </button>
              ))}
            </>
          )}
          
          {/* Other Apps */}
          {getAvailableApps().filter(app => app.app_type !== 'core-tool').length > 0 && (
            <>
              <div className="px-3 py-2 text-xs font-medium text-slate-600 dark:text-slate-300 bg-slate-50 dark:bg-slate-700/50">
                Applications
              </div>
              {getAvailableApps()
                .filter(app => app.app_type !== 'core-tool')
                .map((app) => (
                  <button
                    key={`app-${app.id}`}
                    className="flex items-start w-full px-4 py-3 text-left hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors text-slate-700 dark:text-slate-300"
                    onClick={() => {
                      if (isMounted()) {
                        addTab(app);
                        setShowAppMenu(false);
                      }
                    }}
                  >
                    <FontAwesomeIcon 
                      icon={app.icon} 
                      className={`mr-3 h-4 w-4 mt-0.5 flex-shrink-0 ${
                        app.plugin_type === 'python' ? 'text-yellow-500' : 'text-slate-500 dark:text-slate-400'
                      }`} 
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="font-medium truncate">{app.name}</span>
                        {app.plugin_type === 'python' && (
                          <span className="text-xs bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 px-1.5 py-0.5 rounded">
                            Python
                          </span>
                        )}
                      </div>
                      {app.description && (
                        <div className="text-xs text-slate-500 dark:text-slate-400 mt-1 line-clamp-2">
                          {app.description}
                        </div>
                      )}
                    </div>
                  </button>
                ))}
            </>
          )}
          
          {!loading && getAvailableApps().length === 0 && (
            <div className="px-4 py-6 text-center text-slate-500 dark:text-slate-400">
              <FontAwesomeIcon icon={faWrench} className="h-8 w-8 mb-2 opacity-50" />
              <div className="text-sm">No applications available</div>
            </div>
          )}
      </div>
    );
  };

  // State for filtered apps when searching
  const [searchTerm, setSearchTerm] = useState('');

  // Filter apps based on search term
  const filteredApps = useCallback(() => {
    if (!searchTerm.trim()) return getAvailableApps();
    return getAvailableApps().filter(app => 
      app.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (app.description && app.description.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }, [getAvailableApps, searchTerm]);

  // Render the main tab system UI
  return (
    <ErrorBoundary>
      <div className="flex items-center bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 px-4 z-50" ref={menuRef}>
        {/* Render the tab list */}
        <div className="flex items-center flex-1 overflow-x-auto">
          {renderTabs()}
        </div>
        
        {/* Add tab button - centered standalone dialog */}
        <div className="relative ml-2">
          <Button
            variant="ghost"
            size="sm"
            className="px-3 py-2 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 hover:bg-slate-200 dark:hover:bg-slate-700 transition-colors"
            onClick={() => {
              if (isMounted()) {
                setShowAppMenu(!showAppMenu);
                setSearchTerm(''); // Clear search on toggle
              }
            }}
          >
            <FontAwesomeIcon icon={faPlus} className="h-4 w-4" />
          </Button>
          {showAppMenu && (
            <div className="fixed inset-0 bg-black/30 flex items-center justify-center z-50">
              <div className="bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-600 rounded-lg shadow-xl min-w-[320px] md:min-w-[480px] max-w-[90vw] max-h-[80vh] flex flex-col" onClick={(e) => e.stopPropagation()}>
                <div className="flex justify-between items-center px-4 py-3 border-b border-slate-100 dark:border-slate-700">
                  <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-200">Open Application</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="p-1 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
                    onClick={() => setShowAppMenu(false)}
                  >
                    <FontAwesomeIcon icon={faTimes} className="h-4 w-4" />
                  </Button>
                </div>
                
                {/* Search input */}
                <div className="px-4 py-3 border-b border-slate-100 dark:border-slate-700">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Search applications..."
                      className="w-full px-4 py-2 pr-10 border border-slate-200 dark:border-slate-700 rounded-md bg-slate-50 dark:bg-slate-800/60 text-slate-800 dark:text-slate-200 focus:outline-none focus:ring-3 focus:ring-blue-500/30 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      autoFocus
                    />
                    <FontAwesomeIcon icon={faSearch || faGlobe /* fallback */} className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
                  </div>
                </div>
                
                <div className="flex-1 overflow-y-auto p-2">
                  {loading ? (
                    <div className="flex items-center justify-center py-6">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                      <span className="ml-2 text-sm text-slate-500 dark:text-slate-400">Loading apps...</span>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {filteredApps().map((app) => (
                        <button
                          key={`app-${app.id}`}
                          className="flex items-start w-full p-3 text-left hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors text-slate-700 dark:text-slate-300 rounded-md"
                          onClick={() => {
                            if (isMounted()) {
                              addTab({ 
                                id: app.id, 
                                name: app.name, 
                                icon: app.icon,
                                component: app.component || app.name
                              });
                              setShowAppMenu(false);
                            }
                          }}
                        >
                          <FontAwesomeIcon 
                            icon={app.icon} 
                            className={`mr-3 h-5 w-5 mt-0.5 flex-shrink-0 ${app.app_type === 'core-tool' ? 'text-blue-500 dark:text-blue-400' : app.plugin_type === 'python' ? 'text-yellow-500' : 'text-slate-500 dark:text-slate-400'}`} 
                          />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <span className="font-medium truncate">{app.name}</span>
                              {app.plugin_type === 'python' && (
                                <span className="text-xs bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 px-1.5 py-0.5 rounded">
                                  Python
                                </span>
                              )}
                            </div>
                            {app.description && (
                              <div className="text-xs text-slate-500 dark:text-slate-400 mt-1 line-clamp-2">
                                {app.description}
                              </div>
                            )}
                          </div>
                        </button>
                      ))}
                      
                      {!loading && filteredApps().length === 0 && (
                        <div className="col-span-2 px-4 py-6 text-center text-slate-500 dark:text-slate-400">
                          {searchTerm ? (
                            <>
                              <FontAwesomeIcon icon={faSearch} className="h-8 w-8 mb-2 opacity-50" />
                              <div className="text-sm">No applications found matching "{searchTerm}"</div>
                            </>
                          ) : (
                            <>
                              <FontAwesomeIcon icon={faWrench} className="h-8 w-8 mb-2 opacity-50" />
                              <div className="text-sm">No applications available</div>
                            </>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </ErrorBoundary>
  );
});

TabSystem.displayName = 'TabSystem';

export default TabSystem;